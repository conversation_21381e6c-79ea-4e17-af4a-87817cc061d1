<template>
  <StaticsLayoutCustomer type="preset" :styles="{view:'body'}">
    <StaticsLayoutMain type="view">
      <StaticsTrunkPageTitle
        label="ムードボード名"
        actionType="horizontal"
        :actions="[
          { label: 'ムードボードに戻る', icon: 'arrow-lined-left', link: getMockupRoot()+'/a/e/boc/mbs/0001' },
        ]"
      />
      <StaticsLayoutSection type="default_content">
        <p class="text">
          説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文説明文
        </p>
      </StaticsLayoutSection>

      <StaticsLayoutSection type="boxed_content" label="アイテム一覧">
        <StaticsTrunkActions
          type="sectionActions"
          :buttons="[
            { label: 'リストからアイテムを追加', icon: 'plus', link:getMockupRoot()+'/a/e/boc/mbs/itemAdd' },
            { label: '画像アップロード', icon: 'image', link:getMockupRoot()+'/a/e/boc/mbs/imageUpload' },
            { label: 'メモを編集', icon: 'edit', link: getMockupRoot()+'/a/e/boc/mbs/0001/01/edit' },
          ]"
        />
        <StaticsAggregationCardList
          column="hor-3"
          type="default"
          :styles="{}"
          childType="default"
          :childStyles="{}"
          :cards="[
            { type:'plain', thumbnail: 'dummy/moodboard/01.jpg', }, {
              type:'plain', thumbnail: 'dummy/moodboard/10.jpg', },
            { type:'plain', description: '説明文説明文説明文説明文説明文説明文説明文', thumbnail: 'dummy/moodboard/11.jpg', },
            { type:'plain', description: '説明文説明文説明文説明文説明文説明文説明文', thumbnail: 'dummy/moodboard/12.jpg', },
            { type:'plain', description: '説明文説明文説明文説明文説明文説明文説明文', thumbnail: 'dummy/moodboard/13.jpg', },
            { type:'plain', description: '説明文説明文説明文説明文説明文説明文説明文', thumbnail: 'dummy/moodboard/14.jpg', },
          ]"
        />
      </StaticsLayoutSection>

      <StaticsLayoutSection type="default" :styles="{ height: 'maxContent' }"></StaticsLayoutSection>

      <div class="bottomFixedActions">
        <StaticsAggregationActionGroup
          :actions="[
            { type:'secondary', label: '戻る', link:getMockupRoot()+'/a/e/boc/mbs/0001' },
          ]"
        />
      </div>

    </StaticsLayoutMain>
  </StaticsLayoutCustomer>
</template>
<script>
import chamomileEditors from '~/mixins/chamomile/editors';
import mockupMixin from '~/mixins/mockup/common.js';

export default {
  layout: 'chamomile/admin',
  mixins: [chamomileEditors, mockupMixin],

  methods: {
    // Modal : callback値を
    openModal() {this.$refs.modal.showModal();},
    openModalQr() {this.$refs.modalQr.showModal();},
  },
};
</script>
<style lang="scss" scoped>
@use "~/mixins/chamomile/scss/index" as *;
</style>
